<template>
  <div id="app">
    <h1>Galaxy Vue UI - Development Environment</h1>

    <div class="demo-section">
      <h2>Card Components</h2>
      <div class="component-demo">
        <GCard title="Sample Card" style="width: 300px;">
          <p>This is a sample card content for testing purposes.</p>
          <template #footer>
            <el-button type="primary">Action</el-button>
          </template>
        </GCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { GCard } from '../src'
</script>

<style scoped>
#app {
  padding: 20px;
  font-family: Arial, sans-serif;
}
</style>
