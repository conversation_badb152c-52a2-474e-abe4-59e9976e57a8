import type { App } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import GCard from './components/GCard/index.vue'

// 组件列表
const components = [
  GCard
]

// 定义 install 方法
const install = (app: App): void => {
  app.use(ElementPlus)

  // 注册自定义组件
  components.forEach(component => {
    const name = component.name || component.__name || 'UnknownComponent'
    app.component(name, component)
  })
}

// 导出组件库
export {
  GCard,
  // 安装方法
  install
}

// 默认导出
export default {
  install
}
