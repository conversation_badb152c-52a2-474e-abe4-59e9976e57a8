import type { Meta, StoryObj } from '@storybook/vue3';
import GCard from './index.vue';

const meta: Meta<typeof GCard> = {
  title: 'Components/GCard',
  component: GCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A customizable card component based on Element Plus ElCard with additional features.',
      },
    },
  },
  argTypes: {
    header: {
      control: 'text',
      description: 'Card header text',
    },
    shadow: {
      control: 'select',
      options: ['always', 'hover', 'never'],
      description: 'When to show card shadow',
    },
    bodyStyle: {
      control: 'object',
      description: 'Custom styles for card body',
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    header: 'Default Card',
    shadow: 'always',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args };
    },
    template: `
      <GCard v-bind="args">
        <p>This is the default card content. You can put any content here.</p>
        <p>The card uses Element Plus styling with additional customizations.</p>
      </GCard>
    `,
  }),
};

export const WithHeaderSlot: Story = {
  args: {
    shadow: 'hover',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args };
    },
    template: `
      <GCard v-bind="args">
        <template #header>
          <div style="display: flex; align-items: center; gap: 8px;">
            <span style="font-weight: bold; color: #409eff;">🎯 Custom Header</span>
            <span style="background: #f0f9ff; color: #0369a1; padding: 2px 8px; border-radius: 4px; font-size: 12px;">NEW</span>
          </div>
        </template>
        <p>This card uses a custom header slot with icons and styling.</p>
        <p>The header can contain any Vue template content.</p>
      </GCard>
    `,
  }),
};

export const WithFooter: Story = {
  args: {
    header: 'Card with Footer',
    shadow: 'always',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args };
    },
    template: `
      <GCard v-bind="args">
        <p>This card demonstrates the footer slot functionality.</p>
        <p>You can add action buttons or additional information in the footer.</p>
        <template #footer>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #666; font-size: 14px;">Last updated: 2024-01-01</span>
            <div style="display: flex; gap: 8px;">
              <button style="padding: 4px 12px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">Cancel</button>
              <button style="padding: 4px 12px; border: 1px solid #409eff; border-radius: 4px; background: #409eff; color: white; cursor: pointer;">Confirm</button>
            </div>
          </div>
        </template>
      </GCard>
    `,
  }),
};

export const CustomBodyStyle: Story = {
  args: {
    header: 'Custom Styled Card',
    shadow: 'hover',
    bodyStyle: {
      padding: '24px',
      backgroundColor: '#f8fafc',
      borderRadius: '8px',
    },
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args };
    },
    template: `
      <GCard v-bind="args">
        <div style="text-align: center;">
          <h3 style="margin: 0 0 16px 0; color: #1f2937;">Custom Body Styling</h3>
          <p style="margin: 0; color: #6b7280;">This card has custom body styles applied through the bodyStyle prop.</p>
        </div>
      </GCard>
    `,
  }),
};

export const NoShadow: Story = {
  args: {
    header: 'No Shadow Card',
    shadow: 'never',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args };
    },
    template: `
      <GCard v-bind="args">
        <p>This card has no shadow effect.</p>
        <p>Useful for flat design or when cards are used within other containers.</p>
      </GCard>
    `,
  }),
};

export const MinimalCard: Story = {
  args: {
    shadow: 'hover',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args };
    },
    template: `
      <GCard v-bind="args">
        <p>A minimal card without header or footer.</p>
      </GCard>
    `,
  }),
};
