import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { GCard } from '../../src'

describe('GCard', () => {
  it('renders with content', () => {
    const wrapper = mount(GCard, {
      props: { title: 'Test Card' },
      slots: { default: 'Card content' }
    })

    expect(wrapper.text()).toContain('Card content')
    expect(wrapper.exists()).toBe(true)
  })

  it('renders footer slot', () => {
    const wrapper = mount(GCard, {
      props: { title: 'Test Card' },
      slots: {
        default: 'Card content',
        footer: 'Footer content'
      }
    })

    expect(wrapper.text()).toContain('Footer content')
  })

  it('applies shadow prop correctly', () => {
    const wrapper = mount(GCard, {
      props: { shadow: 'always' }
    })

    expect(wrapper.classes()).toContain('el-card')
  })
})
