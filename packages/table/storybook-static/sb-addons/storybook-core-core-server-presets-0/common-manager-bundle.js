try{
(()=>{var h=__STORYBOOK_API__,{ActiveTabs:g,Consumer:v,ManagerContext:x,Provider:O,RequestResponseError:T,addons:n,combineParameters:U,controlOrMetaKey:w,controlOrMetaSymbol:f,eventMatchesShortcut:A,eventToShortcut:P,experimental_MockUniversalStore:M,experimental_UniversalStore:R,experimental_requestResponse:C,experimental_useUniversalStore:B,isMacLike:E,isShortcutTaken:I,keyToSymbol:K,merge:N,mockChannel:G,optionOrAltSymbol:L,shortcutMatchesShortcut:Y,shortcutToHumanString:q,types:D,useAddonState:F,useArgTypes:H,useArgs:j,useChannel:V,useGlobalTypes:z,useGlobals:J,useParameter:Q,useSharedState:W,useStoryPrepared:X,useStorybookApi:Z,useStorybookState:$}=__STORYBOOK_API__;var m=(()=>{let e;return typeof window<"u"?e=window:typeof globalThis<"u"?e=globalThis:typeof window<"u"?e=window:typeof self<"u"?e=self:e={},e})(),p="tag-filters",_="static-filter";n.register(p,e=>{let d=Object.entries(m.TAGS_OPTIONS??{}).reduce((o,s)=>{let[r,u]=s;return u.excludeFromSidebar&&(o[r]=!0),o},{});e.experimental_setFilter(_,o=>{let s=o.tags??[];return(s.includes("dev")||o.type==="docs")&&s.filter(r=>d[r]).length===0})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
