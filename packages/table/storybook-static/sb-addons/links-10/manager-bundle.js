try{
(()=>{var S=__STORYBOOK_API__,{ActiveTabs:b,Consumer:c,ManagerContext:h,Provider:v,RequestResponseError:g,addons:n,combineParameters:x,controlOrMetaKey:E,controlOrMetaSymbol:O,eventMatchesShortcut:U,eventToShortcut:T,experimental_MockUniversalStore:A,experimental_UniversalStore:R,experimental_requestResponse:w,experimental_useUniversalStore:I,isMacLike:P,isShortcutTaken:C,keyToSymbol:M,merge:D,mockChannel:N,optionOrAltSymbol:B,shortcutMatchesShortcut:K,shortcutToHumanString:V,types:f,useAddonState:q,useArgTypes:G,useArgs:L,useChannel:Y,useGlobalTypes:$,useGlobals:H,useParameter:Q,useSharedState:j,useStoryPrepared:z,useStorybookApi:F,useStorybookState:J}=__STORYBOOK_API__;var e="storybook/links",d={NAVIGATE:`${e}/navigate`,REQUEST:`${e}/request`,RECEIVE:`${e}/receive`};n.register(e,o=>{o.on(d.REQUEST,({kind:a,name:l})=>{let u=o.storyId(a,l);o.emit(d.RECEIVE,u)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
