{"v": 5, "entries": {"components-gtable--docs": {"id": "components-gtable--docs", "title": "Components/GTable", "name": "Docs", "importPath": "./src/components/GTable/GTable.stories.ts", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "components-gtable--default": {"type": "story", "id": "components-gtable--default", "name": "<PERSON><PERSON><PERSON>", "title": "Components/GTable", "importPath": "./src/components/GTable/GTable.stories.ts", "componentPath": "./src/components/GTable/index.vue", "tags": ["dev", "test", "autodocs"]}, "components-gtable--with-row-selection": {"type": "story", "id": "components-gtable--with-row-selection", "name": "With Row Selection", "title": "Components/GTable", "importPath": "./src/components/GTable/GTable.stories.ts", "componentPath": "./src/components/GTable/index.vue", "tags": ["dev", "test", "autodocs"]}, "components-gtable--compact-table": {"type": "story", "id": "components-gtable--compact-table", "name": "Compact Table", "title": "Components/GTable", "importPath": "./src/components/GTable/GTable.stories.ts", "componentPath": "./src/components/GTable/index.vue", "tags": ["dev", "test", "autodocs"]}, "components-gtable--custom-height": {"type": "story", "id": "components-gtable--custom-height", "name": "Custom Height", "title": "Components/GTable", "importPath": "./src/components/GTable/GTable.stories.ts", "componentPath": "./src/components/GTable/index.vue", "tags": ["dev", "test", "autodocs"]}}}