{"generatedAt": 1753090286963, "userSince": 1752647982342, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {"vitest": "3.1.1"}, "hasRouterPackage": false, "monorepo": "Lerna", "packageManager": {"type": "pnpm", "version": "10.12.1", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": true}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "portableStoriesFileCount": 0, "applicationFileCount": 0, "language": "javascript", "storybookPackages": {}, "addons": {"@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-docs": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}, "@storybook/addon-links": {"version": "8.6.14"}}}