import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    dts({
      insertTypesEntry: true,
      copyDtsFiles: false
    })
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'GalaxyVueTable',
      fileName: 'index'
    },
    rollupOptions: {
      external: ['vue', 'ag-grid-community', 'ag-grid-vue3', 'xlsx', '@galaxy-vue/shared'],
      output: {
        globals: {
          vue: 'Vue',
          'ag-grid-community': 'agGrid',
          'ag-grid-vue3': 'AgGridVue3',
          xlsx: 'XLSX',
          '@galaxy-vue/shared': 'GalaxyVueShared',
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@galaxy-vue/shared': resolve(__dirname, '../shared/src'),
    }
  }
})
