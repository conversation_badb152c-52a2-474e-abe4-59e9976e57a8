import { config } from '@vue/test-utils'
import { vi } from 'vitest'

// Global test setup for table components
config.global.stubs = {
  'ag-grid-vue': true
}

// Mock AG Grid to avoid issues in tests
global.window = global.window || {}
global.window.HTMLElement = global.window.HTMLElement || class {}

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  warn: vi.fn(),
  error: vi.fn(),
}
