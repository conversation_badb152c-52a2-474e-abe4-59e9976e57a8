import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import GTable from './index.vue';
import type { TableColumn, TableData } from '@/types';

// Sample data for stories
const sampleColumns: TableColumn[] = [
  { field: 'id', headerName: 'ID', width: 80, sortable: true },
  { field: 'name', headerName: 'Name', width: 150, sortable: true, filter: true },
  { field: 'email', headerName: 'Email', width: 200, sortable: true, filter: true },
  { field: 'age', headerName: 'Age', width: 100, sortable: true, filter: 'agNumberColumnFilter' },
  { field: 'department', headerName: 'Department', width: 150, sortable: true, filter: true },
  { field: 'salary', headerName: 'Salary', width: 120, sortable: true, filter: 'agNumberColumnFilter' },
];

const sampleData: TableData[] = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', age: 30, department: 'Engineering', salary: 75000 },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', age: 28, department: 'Design', salary: 68000 },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', age: 35, department: 'Marketing', salary: 62000 },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>', age: 32, department: 'Engineering', salary: 78000 },
  { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', age: 29, department: 'Sales', salary: 55000 },
  { id: 6, name: 'Diana Davis', email: '<EMAIL>', age: 31, department: 'HR', salary: 58000 },
  { id: 7, name: 'Edward Miller', email: '<EMAIL>', age: 33, department: 'Engineering', salary: 82000 },
  { id: 8, name: 'Fiona Garcia', email: '<EMAIL>', age: 27, department: 'Design', salary: 65000 },
];

const meta: Meta<typeof GTable> = {
  title: 'Components/GTable',
  component: GTable,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A powerful data table component built on AG Grid with advanced features like sorting, filtering, pagination, and Excel export.',
      },
    },
  },
  argTypes: {
    columnDefs: {
      control: 'object',
      description: 'Column definitions for the table',
    },
    rowData: {
      control: 'object',
      description: 'Data to display in the table',
    },
    height: {
      control: 'text',
      description: 'Height of the table (string or number)',
    },
    pagination: {
      control: 'boolean',
      description: 'Enable pagination',
    },
    paginationPageSize: {
      control: 'number',
      description: 'Number of rows per page',
    },
    rowSelection: {
      control: 'object',
      description: 'Row selection configuration',
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    columnDefs: sampleColumns,
    rowData: sampleData,
    height: '400px',
    pagination: true,
    paginationPageSize: 10,
  },
  render: (args) => ({
    components: { GTable },
    setup() {
      return { args };
    },
    template: `
      <div style="padding: 20px;">
        <h3>Default Table</h3>
        <p>A basic table with sorting, filtering, and pagination enabled.</p>
        <GTable v-bind="args" />
      </div>
    `,
  }),
};

export const WithRowSelection: Story = {
  args: {
    columnDefs: sampleColumns,
    rowData: sampleData,
    height: '400px',
    pagination: true,
    paginationPageSize: 5,
    rowSelection: {
      type: 'multiple',
      suppressRowClickSelection: false
    },
  },
  render: (args) => ({
    components: { GTable },
    setup() {
      const handleSelectionChanged = (selectedRows: TableData[]) => {
        console.log('Selected rows:', selectedRows);
      };
      
      return { args, handleSelectionChanged };
    },
    template: `
      <div style="padding: 20px;">
        <h3>Table with Row Selection</h3>
        <p>Click on rows to select them. Multiple selection is enabled.</p>
        <GTable v-bind="args" @selection-changed="handleSelectionChanged" />
      </div>
    `,
  }),
};

export const CompactTable: Story = {
  args: {
    columnDefs: sampleColumns.slice(0, 4), // Show fewer columns
    rowData: sampleData.slice(0, 5), // Show fewer rows
    height: '300px',
    pagination: false,
  },
  render: (args) => ({
    components: { GTable },
    setup() {
      return { args };
    },
    template: `
      <div style="padding: 20px;">
        <h3>Compact Table</h3>
        <p>A smaller table without pagination, showing limited data.</p>
        <GTable v-bind="args" />
      </div>
    `,
  }),
};

export const CustomHeight: Story = {
  args: {
    columnDefs: sampleColumns,
    rowData: sampleData,
    height: 600,
    pagination: true,
    paginationPageSize: 20,
  },
  render: (args) => ({
    components: { GTable },
    setup() {
      return { args };
    },
    template: `
      <div style="padding: 20px;">
        <h3>Custom Height Table</h3>
        <p>A taller table with more rows per page.</p>
        <GTable v-bind="args" />
      </div>
    `,
  }),
};
