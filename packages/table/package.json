{"name": "@galaxy-vue/table", "version": "0.1.0", "description": "Galaxy Vue Table - Advanced table components with AG Grid and Excel export", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./style": "./dist/style.css"}, "files": ["dist"], "scripts": {"dev": "vite --config dev/vite.config.ts", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.ts,.jsx,.tsx --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6007", "build-storybook": "storybook build"}, "keywords": ["vue", "vue3", "table", "ag-grid", "excel", "export", "galaxy-vue"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-username/galaxy-vue.git", "directory": "packages/table"}, "peerDependencies": {"vue": "^3.4.0", "ag-grid-community": "^34.0.2", "ag-grid-vue3": "^34.0.2", "xlsx": "^0.18.5"}, "dependencies": {"ag-grid-community": "^34.0.2", "ag-grid-vue3": "^34.0.2", "xlsx": "^0.18.5", "@galaxy-vue/shared": "workspace:*"}, "devDependencies": {"@types/node": "^22.15.31", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "sass": "^1.87.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.0", "vitest": "^3.1.1", "vue": "^3.5.13", "vue-tsc": "^2.2.10"}}