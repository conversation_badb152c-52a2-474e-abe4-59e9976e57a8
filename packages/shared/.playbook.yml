# Galaxy Vue Shared Package Playbook Configuration
# This file defines workflows specific to the shared utilities package

name: "@galaxy-vue/shared"
version: "0.1.0"
description: "Galaxy Vue Shared - Common utilities, types, and constants"
type: "library"

# Package-specific environment
env:
  PACKAGE_NAME: "@galaxy-vue/shared"
  BUILD_TARGET: "es2020"
  OUTPUT_DIR: "dist"

# Dependencies specific to this package
dependencies:
  - typescript
  - vite
  - vite-plugin-dts
  - vitest

# Package-specific scripts
scripts:
  # Development
  dev:
    description: "Start development mode with watch"
    steps:
      - name: "Build in watch mode"
        run: "vite build --watch"

  # Building
  build:
    description: "Build the shared package"
    steps:
      - name: "Clean previous build"
        run: "rm -rf dist"
      - name: "Type check"
        run: "vue-tsc --noEmit"
      - name: "Build with Vite"
        run: "vite build"
      - name: "Verify build output"
        run: "ls -la dist/"

  build:types:
    description: "Generate TypeScript declarations"
    steps:
      - name: "Generate .d.ts files"
        run: "vue-tsc --declaration --emitDeclarationOnly --outDir dist"

  # Testing
  test:
    description: "Run unit tests"
    steps:
      - name: "Run Vitest"
        run: "vitest run"

  test:watch:
    description: "Run tests in watch mode"
    steps:
      - name: "Run Vitest in watch mode"
        run: "vitest"

  test:ui:
    description: "Run tests with UI"
    steps:
      - name: "Run Vitest UI"
        run: "vitest --ui"

  test:coverage:
    description: "Run tests with coverage"
    steps:
      - name: "Generate coverage report"
        run: "vitest --coverage"

  # Quality assurance
  lint:
    description: "Lint TypeScript files"
    steps:
      - name: "Run ESLint"
        run: "eslint src --ext .js,.ts --fix"

  type-check:
    description: "Check TypeScript types"
    steps:
      - name: "Run TypeScript compiler"
        run: "vue-tsc --noEmit"

  # Maintenance
  clean:
    description: "Clean build artifacts"
    steps:
      - name: "Remove dist directory"
        run: "rm -rf dist"
      - name: "Remove coverage directory"
        run: "rm -rf coverage"

# Build configuration
build:
  entry: "src/index.ts"
  output: "dist"
  formats: ["es", "cjs"]
  external: []
  
# Test configuration
test:
  framework: "vitest"
  environment: "node"
  coverage:
    provider: "v8"
    reporter: ["text", "html", "lcov"]
    threshold:
      statements: 80
      branches: 80
      functions: 80
      lines: 80

# Linting configuration
lint:
  files: ["src/**/*.ts", "src/**/*.js"]
  ignore: ["dist/**/*", "coverage/**/*"]

# Type checking
typescript:
  config: "tsconfig.json"
  strict: true
  check_unused: true

# Pre-commit hooks for this package
hooks:
  pre-commit:
    - name: "Lint files"
      run: "eslint src --ext .js,.ts"
    - name: "Type check"
      run: "vue-tsc --noEmit"
    - name: "Run tests"
      run: "vitest run"

# Package validation
validation:
  build_required: true
  tests_required: true
  types_required: true
  
# Export configuration
exports:
  ".": {
    "types": "./dist/index.d.ts",
    "import": "./dist/index.js"
  }

# Publishing configuration
publish:
  registry: "https://registry.npmjs.org/"
  access: "public"
  files: ["dist"]
  
# Documentation
docs:
  api_docs: true
  examples: false
  readme: true

# Monitoring
monitoring:
  bundle_size: true
  performance: true
  dependencies: true
