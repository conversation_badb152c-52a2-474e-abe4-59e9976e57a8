# Galaxy Vue - 快速开始指南

## 项目概述

Galaxy Vue 是一个基于 Vue 3 的现代组件库脚手架，包含两个主要包：

- **@galaxy-vue/ui** - 基于 Element Plus 的二次封装组件库
- **@galaxy-vue/table** - 结合 AG Grid 和 Excel 导出功能的高级表格组件

## 项目结构

```
galaxy-vue/
├── packages/
│   ├── ui/                     # @galaxy-vue/ui 包
│   │   ├── src/
│   │   │   ├── components/     # 组件目录
│   │   │   │   ├── GButton/    # 按钮组件
│   │   │   │   └── GCard/      # 卡片组件
│   │   │   ├── index.ts        # 主入口文件
│   │   │   ├── main.ts         # 开发入口
│   │   │   └── App.vue         # 开发示例页面
│   │   ├── package.json
│   │   ├── vite.config.ts
│   │   └── tsconfig.json
│   └── table/                  # @galaxy-vue/table 包
│       ├── src/
│       │   ├── components/     # 组件目录
│       │   │   ├── GTable/     # 表格组件
│       │   │   └── GExcelExport/ # Excel导出组件
│       │   ├── utils/          # 工具函数
│       │   │   └── excel.ts    # Excel处理工具
│       │   ├── types/          # 类型定义
│       │   └── index.ts        # 主入口文件
│       ├── package.json
│       ├── vite.config.ts
│       └── tsconfig.json
├── package.json                # 根包配置
├── pnpm-workspace.yaml         # pnpm workspace 配置
├── lerna.json                  # lerna 配置
├── tsconfig.json               # TypeScript 配置
└── README.md                   # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 开发模式

启动 UI 组件开发服务器：

```bash
pnpm dev
```

访问 http://localhost:5173 查看组件示例。

### 3. 构建

构建所有包：

```bash
pnpm build
```

构建单个包：

```bash
pnpm build:ui      # 构建 UI 包
pnpm build:table   # 构建 Table 包
```

## 组件使用示例

## **项目结构与治理**

本章定义了组件库项目本身的宏观结构，包括代码仓库的组织方式、贡献流程和版本管理策略，旨在建立一个高效、透明且自动化的开发体系。

### **Monorepo 架构：pnpm Workspaces 与 Vite**

为了有效管理一个包含多个相互关联包（如核心组件库、文档网站、工具函数等）的复杂项目，我们将采用 **Monorepo（单一代码仓库）架构**
43。

* **包管理器**：我们选用 **pnpm** 作为唯一的包管理器。pnpm 的 workspaces 功能和其基于符号链接的非扁平化 node\_modules
  结构，使其在 Monorepo 场景下具有显著优势，包括极致的磁盘空间效率和更快的安装速度。
* **目录结构**：在项目根目录下，将创建一个 pnpm-workspace.yaml 文件来定义工作区。标准的目录结构如下：
  YAML
  packages:
  \- 'packages/\*' \# 用于存放可发布的包，如 @galaxy-vue/shared, @galaxy-vue/ui

* **构建工具**：**Vite** 作为主要的构建和开发工具。根目录下的 vite.config.ts 将提供共享的基础配置，而每个独立的包或应用可以在其自己的目录中创建
  vite.config.ts 来扩展或覆盖这些配置，以满足特定需求。

### **贡献工作流：分支、PR 与代码审查**

一个清晰的贡献流程是保证代码质量和促进社区协作的关键。我们将采纳一个类 GitFlow 的简化分支模型，并结合标准的 GitHub Pull
Request (PR) 工作流。

* **分支策略**：
  * main 分支：作为**最新稳定发布**分支。所有用于修复已发布版本 bug 的 PR 都应提交到 main。
* **PR 流程**：
  1. 贡献者首先 Fork 官方仓库。
  2. 根据开发类型（新功能或 bug 修复），从 dev 或 main 分支创建自己的特性分支。
  3. 完成开发和测试后，向官方仓库的相应分支（main）提交 Pull Request。
  4. 所有 PR 都必须通过所有自动化 CI 检查（如 linting 和 testing）。
  5. PR 必须经过至少一名核心团队成员的**代码审查 (Code Review)** 并获得批准后，方可合并。

### **Commit Message 规范：采用 Conventional Commits**

为了保持 Git 提交历史的清晰、可读和自动化处理，所有提交信息**必须**遵循 **Conventional Commits** 规范 53。Element Plus
等许多大型项目都采用了此规范 55。

* **格式**：\<type\>(\<scope\>): \<description\>
  * type: 提交类型，如 feat (新功能), fix (bug 修复), docs (文档), refactor (重构) 等 56。
  * scope: (可选) 影响范围，通常是组件名，如 button, modal。
  * description: 简明扼要的提交描述。
* **自动化价值**：采用 Conventional Commits 不仅仅是为了美化日志。它是实现**自动化发布流程**的关键一环。当一个 PR
  被合并后，CI/CD 流水线可以自动分析其中的 commit messages：
  1. 如果检测到 feat 类型的提交，工具（如 semantic-release 53）会自动将版本号进行次版本升级（minor bump, e.g.,
     1.2.5 \-\> 1.3.0）。
  2. 如果只检测到 fix 类型的提交，则进行补丁版本升级（patch bump, e.g., 1.2.5 \-\> 1.2.6）。
  3. 如果检测到 BREAKING CHANGE: 标记，则进行主版本升级（major bump, e.g., 1.2.5 \-\> 2.0.0）。
  4. 同时，工具还能根据这些信息自动生成 CHANGELOG.md 文件，创建 Git tag, 并将更新后的包发布到 npm。
     这个从“规范化提交”到“自动化发布”的流程，形成了一个强大的效率飞轮，极大地减少了人工维护版本和发布日志的负担。
* **强制执行**：为了确保规范的严格执行，我们将使用 commitlint 和
  husky 工具。husky 会设置一个 pre-commit 或 commit-msg git 钩子，在每次提交时调用 commitlint 进行校验，不符合规范的提交将被直接拒绝。

### **实时文档与交互式风格指南**

静态的 API 文档已无法满足现代开发者的需求。我们必须提供一个“活的”、可交互的文档网站。

* **定位**：文档网站本身将作为 Monorepo 内的一个独立应用（位于 apps/docs），使用我们自己的组件库来构建，这本身就是对组件库的最佳实践展示（Dogfooding）。
* **核心功能**：
  * **组件浏览器**：清晰地展示所有可用组件。
  * **交互式 Playground**：每个组件的文档页都必须包含一个交互式示例区。用户可以在浏览器中直接修改组件的 props，并实时看到渲染结果的变化 58。这极大地降低了学习和调试成本。
  * **代码示例**：提供清晰、可一键复制的常见用例代码片段。
  * **API 表格**：详细列出每个组件的 Props、Events、Slots 和公开的方法。理想情况下，这些信息应通过 TSDoc 或类似工具从源代码注释中自动生成，以保证文档与代码的同步。

### **使用 Linter 和 Formatter 保证代码质量**

为了确保代码风格的一致性并及早发现潜在问题，我们将强制使用代码检查和格式化工具。

* **ESLint**：用于代码质量和风格检查。我们将配置 eslint-plugin-vue 的推荐规则集，并根据项目需求进行微调，以强制执行本规范中定义的编码标准 39。
* **Prettier**：用于代码自动格式化。我们将定义一套统一的格式化规则（如缩进、引号、行尾逗号等），以终结所有关于代码格式的个人偏好争论 52。
* **自动化执行**：为了将这些工具无缝集成到开发流程中，我们将使用 husky 和 lint-staged 39。
  * husky 会在开发者执行 git commit 命令时触发一个 pre-commit 钩子。
  * 该钩子会调用 lint-staged，lint-staged 则只会对当前暂存区（staged）的文件执行 ESLint 检查和 Prettier 格式化。
  * 这个自动化流程确保了任何提交到代码库的代码都至少经过了基本的质量和格式检查，从而从源头上保证了代码库的整洁。
