# Galaxy Vue Monorepo Playbook Configuration
# This file defines automated workflows for the entire monorepo

name: galaxy-vue-monorepo
version: "1.0.0"
description: "Galaxy Vue Component Library - Automated Development Workflows"

# Global environment variables
env:
  NODE_VERSION: "18"
  PNPM_VERSION: "8"
  REGISTRY_URL: "https://registry.npmjs.org/"

# Global dependencies and tools
dependencies:
  - pnpm@8
  - node@18
  - typescript
  - eslint
  - prettier
  - husky
  - lint-staged
  - commitlint

# Workspace configuration
workspace:
  type: "pnpm"
  packages:
    - "packages/*"
  
# Global scripts that can be run from root
scripts:
  # Development workflows
  setup:
    description: "Setup development environment"
    steps:
      - name: "Install dependencies"
        run: "pnpm install"
      - name: "Setup git hooks"
        run: "pnpm prepare"
      - name: "Build shared package first"
        run: "pnpm build:shared"

  dev:
    description: "Start development server"
    steps:
      - name: "Start UI development server"
        run: "pnpm dev:ui"
        background: true
      - name: "Start Table development server"
        run: "pnpm dev:table"
        background: true

  # Build workflows
  build:
    description: "Build all packages"
    steps:
      - name: "Clean previous builds"
        run: "pnpm clean"
      - name: "Build shared package"
        run: "pnpm build:shared"
      - name: "Build UI package"
        run: "pnpm build:ui"
      - name: "Build Table package"
        run: "pnpm build:table"

  build:parallel:
    description: "Build all packages in parallel"
    steps:
      - name: "Build shared first"
        run: "pnpm build:shared"
      - name: "Build UI and Table in parallel"
        run: "pnpm run --parallel build"
        filter: "!@galaxy-vue/shared"

  # Quality assurance workflows
  lint:
    description: "Lint all packages"
    steps:
      - name: "Run ESLint"
        run: "pnpm lint"
      - name: "Check TypeScript"
        run: "pnpm type-check"

  lint:fix:
    description: "Fix linting issues"
    steps:
      - name: "Fix ESLint issues"
        run: "pnpm lint:fix"
      - name: "Format with Prettier"
        run: "pnpm format"

  test:
    description: "Run all tests"
    steps:
      - name: "Run unit tests"
        run: "pnpm test"
      - name: "Run coverage"
        run: "pnpm test:coverage"

  # Release workflows
  version:
    description: "Version packages"
    steps:
      - name: "Run tests"
        run: "pnpm test"
      - name: "Build packages"
        run: "pnpm build"
      - name: "Version with Lerna"
        run: "pnpm version"

  publish:
    description: "Publish packages"
    steps:
      - name: "Publish with Lerna"
        run: "pnpm publish"

  # Maintenance workflows
  clean:
    description: "Clean all build artifacts"
    steps:
      - name: "Clean dist folders"
        run: "pnpm clean"
      - name: "Clean node_modules"
        run: "rm -rf node_modules packages/*/node_modules"

  reset:
    description: "Reset to clean state"
    steps:
      - name: "Clean all"
        run: "pnpm clean"
      - name: "Remove lock file"
        run: "rm -f pnpm-lock.yaml"
      - name: "Reinstall"
        run: "pnpm install"

# Pre-commit hooks configuration
hooks:
  pre-commit:
    - name: "Lint staged files"
      run: "lint-staged"
    - name: "Type check"
      run: "pnpm type-check"

  commit-msg:
    - name: "Validate commit message"
      run: "commitlint --edit"

# CI/CD pipeline configuration
ci:
  triggers:
    - push
    - pull_request
  
  jobs:
    test:
      name: "Test and Build"
      runs-on: "ubuntu-latest"
      steps:
        - checkout
        - setup-node: "18"
        - install-pnpm
        - cache-dependencies
        - run: "pnpm install"
        - run: "pnpm lint"
        - run: "pnpm test"
        - run: "pnpm build"
    
    release:
      name: "Release"
      runs-on: "ubuntu-latest"
      if: "branch == 'main'"
      needs: ["test"]
      steps:
        - checkout
        - setup-node: "18"
        - install-pnpm
        - run: "pnpm install"
        - run: "pnpm build"
        - run: "pnpm publish"

# Package-specific configurations
packages:
  "@galaxy-vue/shared":
    path: "packages/shared"
    type: "library"
    build_order: 1
    
  "@galaxy-vue/ui":
    path: "packages/ui"
    type: "library"
    dependencies: ["@galaxy-vue/shared"]
    build_order: 2
    
  "@galaxy-vue/table":
    path: "packages/table"
    type: "library"
    dependencies: ["@galaxy-vue/shared"]
    build_order: 2

# Development tools configuration
tools:
  eslint:
    config: ".eslintrc.cjs"
    ignore: ".eslintignore"
  
  prettier:
    config: ".prettierrc"
    ignore: ".prettierignore"
  
  typescript:
    config: "tsconfig.json"
  
  commitlint:
    config: "commitlint.config.js"

# Documentation configuration
docs:
  auto_generate: true
  output_dir: "docs"
  include_api: true
  include_examples: true

# Monitoring and notifications
monitoring:
  build_status: true
  test_coverage: true
  dependency_updates: true

# Security configuration
security:
  audit_dependencies: true
  check_vulnerabilities: true
  auto_update_patches: true
